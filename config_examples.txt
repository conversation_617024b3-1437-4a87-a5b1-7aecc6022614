# Bitcoin Advanced Analyzer - 配置範例

## 配置範例 1: 短線交易設定 (1小時-4小時圖表)

### 移動平均線設定
- 顯示移動平均線: true
- SMA 週期: 10
- EMA 週期: 12
- 上漲趨勢顏色: #00FF00 (綠色)
- 下跌趨勢顏色: #FF0000 (紅色)

### 布林線設定
- 顯示布林線: true
- 布林線週期: 15
- 標準差倍數: 1.8

### RSI 設定
- 顯示 RSI: true
- RSI 週期: 10
- 超買水準: 75
- 超賣水準: 25

### MACD 設定
- 顯示 MACD: true
- 快線週期: 8
- 慢線週期: 17
- 信號線週期: 6

### 支撐阻力設定
- 顯示支撐阻力: true
- 樞軸點週期: 7
- 支撐阻力強度: 2

### K線形態設定
- 顯示K線形態: true
- 形態敏感度: 0.15

### 比特幣專屬設定
- 波動性閾值: 3.0%
- 成交量異常倍數: 1.8
- 盤整區域範圍: 1.5%
- 回看K線數量: 15

---

## 配置範例 2: 中線交易設定 (4小時-日線圖表)

### 移動平均線設定
- 顯示移動平均線: true
- SMA 週期: 20
- EMA 週期: 21
- 上漲趨勢顏色: #0080FF (藍色)
- 下跌趨勢顏色: #FF4000 (橙紅色)

### 布林線設定
- 顯示布林線: true
- 布林線週期: 20
- 標準差倍數: 2.0

### RSI 設定
- 顯示 RSI: true
- RSI 週期: 14
- 超買水準: 70
- 超賣水準: 30

### MACD 設定
- 顯示 MACD: true
- 快線週期: 12
- 慢線週期: 26
- 信號線週期: 9

### 支撐阻力設定
- 顯示支撐阻力: true
- 樞軸點週期: 10
- 支撐阻力強度: 3

### K線形態設定
- 顯示K線形態: true
- 形態敏感度: 0.1

### 比特幣專屬設定
- 波動性閾值: 5.0%
- 成交量異常倍數: 2.0
- 盤整區域範圍: 2.0%
- 回看K線數量: 20

---

## 配置範例 3: 長線投資設定 (日線-週線圖表)

### 移動平均線設定
- 顯示移動平均線: true
- SMA 週期: 50
- EMA 週期: 55
- 上漲趨勢顏色: #008000 (深綠色)
- 下跌趨勢顏色: #800000 (深紅色)

### 布林線設定
- 顯示布林線: true
- 布林線週期: 30
- 標準差倍數: 2.2

### RSI 設定
- 顯示 RSI: true
- RSI 週期: 21
- 超買水準: 65
- 超賣水準: 35

### MACD 設定
- 顯示 MACD: true
- 快線週期: 15
- 慢線週期: 35
- 信號線週期: 12

### 支撐阻力設定
- 顯示支撐阻力: true
- 樞軸點週期: 15
- 支撐阻力強度: 5

### K線形態設定
- 顯示K線形態: true
- 形態敏感度: 0.08

### 比特幣專屬設定
- 波動性閾值: 8.0%
- 成交量異常倍數: 2.5
- 盤整區域範圍: 3.0%
- 回看K線數量: 30

---

## 配置範例 4: 高波動性市場設定

### 移動平均線設定
- 顯示移動平均線: true
- SMA 週期: 15
- EMA 週期: 18
- 上漲趨勢顏色: #00FFFF (青色)
- 下跌趨勢顏色: #FF00FF (洋紅色)

### 布林線設定
- 顯示布林線: true
- 布林線週期: 18
- 標準差倍數: 2.5

### RSI 設定
- 顯示 RSI: true
- RSI 週期: 12
- 超買水準: 80
- 超賣水準: 20

### MACD 設定
- 顯示 MACD: true
- 快線週期: 10
- 慢線週期: 22
- 信號線週期: 7

### 支撐阻力設定
- 顯示支撐阻力: true
- 樞軸點週期: 8
- 支撐阻力強度: 2

### K線形態設定
- 顯示K線形態: true
- 形態敏感度: 0.2

### 比特幣專屬設定
- 波動性閾值: 10.0%
- 成交量異常倍數: 3.0
- 盤整區域範圍: 4.0%
- 回看K線數量: 12

---

## 配置範例 5: 保守型設定 (低風險偏好)

### 移動平均線設定
- 顯示移動平均線: true
- SMA 週期: 30
- EMA 週期: 35
- 上漲趨勢顏色: #228B22 (森林綠)
- 下跌趨勢顏色: #B22222 (火磚紅)

### 布林線設定
- 顯示布林線: true
- 布林線週期: 25
- 標準差倍數: 1.8

### RSI 設定
- 顯示 RSI: true
- RSI 週期: 18
- 超買水準: 65
- 超賣水準: 35

### MACD 設定
- 顯示 MACD: true
- 快線週期: 14
- 慢線週期: 30
- 信號線週期: 10

### 支撐阻力設定
- 顯示支撐阻力: true
- 樞軸點週期: 12
- 支撐阻力強度: 4

### K線形態設定
- 顯示K線形態: true
- 形態敏感度: 0.05

### 比特幣專屬設定
- 波動性閾值: 3.0%
- 成交量異常倍數: 1.5
- 盤整區域範圍: 1.0%
- 回看K線數量: 25

---

## 使用建議

### 選擇配置的指導原則:

1. **交易頻率**
   - 高頻交易: 使用短線設定
   - 中頻交易: 使用中線設定
   - 低頻交易: 使用長線設定

2. **風險承受能力**
   - 高風險: 使用高波動性設定
   - 中風險: 使用中線設定
   - 低風險: 使用保守型設定

3. **市場條件**
   - 牛市: 降低超買水準，提高超賣水準
   - 熊市: 提高超買水準，降低超賣水準
   - 橫盤: 使用較窄的布林線設定

4. **時間框架匹配**
   - 1分鐘-15分鐘: 極短線設定
   - 1小時-4小時: 短線設定
   - 日線-週線: 中長線設定

### 自訂調整提示:

1. **回測驗證**: 使用歷史數據測試配置效果
2. **逐步調整**: 一次只調整一個參數
3. **記錄結果**: 保存有效的配置組合
4. **定期檢視**: 根據市場變化調整參數

### 警報設定建議:

- **重要信號**: RSI極值、MACD交叉、重大波動
- **確認信號**: K線形態、布林線突破
- **風險信號**: 異常成交量、高波動性

記住：沒有一種配置適用於所有市場條件，
成功的交易需要根據市場環境靈活調整策略。
