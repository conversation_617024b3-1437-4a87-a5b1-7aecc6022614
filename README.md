# Bitcoin Advanced Analyzer - Pine Script 指南

## 概述
這是一個專為比特幣交易分析設計的綜合 Pine Script 指標，包含多種技術分析工具、K線形態偵測、支撐阻力識別和比特幣專屬功能。

## 主要功能

### 1. 技術指標
- **移動平均線 (SMA/EMA)**：可自訂週期的簡單和指數移動平均線
- **布林線**：包含上軌、下軌和中軌，可調整標準差倍數
- **RSI (相對強弱指數)**：14週期預設，可自訂超買超賣水準
- **MACD**：移動平均收斂/發散指標，包含信號線和柱狀圖
- **VWAP**：成交量加權平均價格
- **ATR**：平均真實範圍，衡量波動性

### 2. K線形態偵測
自動識別以下經典K線形態：
- **十字星 (Doji)**：開盤價與收盤價幾乎相等
- **鎚子線 (Hammer)**：下影線長，實體小，出現在下跌趨勢中
- **射擊之星 (Shooting Star)**：上影線長，實體小，出現在上漲趨勢中
- **看漲吞沒 (Bullish Engulfing)**：大陽線完全包含前一根陰線
- **看跌吞沒 (Bearish Engulfing)**：大陰線完全包含前一根陽線

### 3. 支撐阻力系統
- **自動樞軸點識別**：基於高點和低點的樞軸分析
- **動態支撐阻力線**：自動繪製水平支撐和阻力線
- **視覺化標記**：使用不同顏色和樣式區分支撐和阻力

### 4. 區域分析
- **盤整區域偵測**：識別價格在特定範圍內的橫盤整理
- **突破區域標記**：標示潛在的突破點位
- **陰影區域**：為關鍵價格水準創建視覺化區域

### 5. 比特幣專屬功能
- **波動性分析**：監控比特幣特有的高波動性特徵
- **成交量異常偵測**：識別異常成交量放大
- **重大價格波動警報**：當價格變動超過設定閾值時發出警報
- **高波動性背景**：在高波動期間改變圖表背景顏色

## 安裝和使用

### 在 TradingView 上安裝
1. 登入您的 TradingView 帳戶
2. 開啟 Pine Script 編輯器 (Alt + E)
3. 複製 `bitcoin_analyzer.pine` 中的完整程式碼
4. 貼上到編輯器中
5. 點擊「儲存」並為指標命名
6. 點擊「添加到圖表」

### 基本設定
1. **選擇時間框架**：建議使用 1小時、4小時或日線圖表
2. **調整參數**：根據您的交易風格調整各項參數
3. **啟用警報**：設定您關心的警報條件

## 參數配置

### 移動平均線設定
- `顯示移動平均線`：開啟/關閉移動平均線顯示
- `SMA 週期`：簡單移動平均線週期 (預設: 20)
- `EMA 週期`：指數移動平均線週期 (預設: 21)
- `上漲/下跌趨勢顏色`：自訂移動平均線顏色

### 布林線設定
- `顯示布林線`：開啟/關閉布林線顯示
- `布林線週期`：計算週期 (預設: 20)
- `標準差倍數`：布林線寬度 (預設: 2.0)

### RSI 設定
- `RSI 週期`：計算週期 (預設: 14)
- `超買水準`：超買閾值 (預設: 70)
- `超賣水準`：超賣閾值 (預設: 30)

### MACD 設定
- `快線週期`：快速EMA週期 (預設: 12)
- `慢線週期`：慢速EMA週期 (預設: 26)
- `信號線週期`：信號線週期 (預設: 9)

### 支撐阻力設定
- `樞軸點週期`：樞軸點計算週期 (預設: 10)
- `支撐阻力強度`：線條強度設定 (預設: 3)

### K線形態設定
- `形態敏感度`：形態識別的敏感度 (預設: 0.1)

### 比特幣專屬設定
- `波動性閾值`：觸發高波動警報的百分比 (預設: 5%)
- `成交量異常倍數`：成交量異常的倍數 (預設: 2.0)
- `盤整區域範圍`：識別盤整的價格範圍 (預設: 2%)

## 視覺化元素

### 圖表上的標記
- 🟡 十字星：黃色十字
- 🟢 鎚子線：綠色向上三角形
- 🔴 射擊之星：紅色向下三角形
- ⬆️ 看漲吞沒：綠色向上箭頭
- ⬇️ 看跌吞沒：紅色向下箭頭
- 🚩 重大波動：大型旗幟標記

### 線條和區域
- **藍色/紅色線**：移動平均線 (趨勢方向)
- **灰色線**：布林線上下軌
- **橙色線**：VWAP
- **紅色虛線**：阻力線
- **綠色虛線**：支撐線
- **紫色區域**：盤整區域
- **橙色背景**：高波動性期間

### 資訊表格
右上角顯示即時資訊：
- RSI 數值和狀態
- MACD 數值和趨勢
- 當前波動性
- 成交量比率

## 警報設定

腳本包含以下預設警報條件：
1. RSI 超買/超賣警報
2. MACD 金叉/死叉信號
3. 重大價格波動警報
4. 布林線突破警報
5. K線形態警報

### 設定警報步驟
1. 右鍵點擊圖表
2. 選擇「添加警報」
3. 選擇「Bitcoin Advanced Analyzer」
4. 選擇所需的警報條件
5. 設定通知方式 (郵件、手機推送等)

## 最佳實踐

### 交易建議
1. **多時間框架分析**：結合不同時間框架確認信號
2. **風險管理**：設定止損和止盈水準
3. **成交量確認**：重要信號應伴隨成交量放大
4. **趨勢確認**：使用移動平均線確認整體趨勢

### 參數調整建議
- **短線交易**：縮短所有週期參數
- **長線投資**：延長週期參數，降低敏感度
- **高波動市場**：提高波動性閾值
- **低波動市場**：降低波動性閾值

## 注意事項

1. **回測重要性**：使用歷史數據測試參數設定
2. **市場適應性**：定期檢視和調整參數
3. **風險警告**：技術分析不保證未來表現
4. **資金管理**：永遠不要投入超過您能承受損失的資金

## 技術支援

如果您在使用過程中遇到問題：
1. 檢查 Pine Script 版本是否為 v6
2. 確認所有參數設定合理
3. 檢查圖表時間框架是否適當
4. 確保有足夠的歷史數據進行計算

## 更新日誌

### v1.0 (當前版本)
- 初始發布
- 包含所有核心功能
- 支援比特幣專屬分析
- 完整的警報系統
