//@version=6
indicator("Bitcoin Advanced Analyzer", shorttitle="BTC Analyzer", overlay=true, max_boxes_count=500, max_lines_count=500)

// ==================== 輸入參數設定 ====================
// 移動平均線設定
ma_group = "移動平均線設定"
show_ma = input.bool(true, "顯示移動平均線", group=ma_group)
sma_length = input.int(20, "SMA 週期", minval=1, group=ma_group)
ema_length = input.int(21, "EMA 週期", minval=1, group=ma_group)
ma_color_bull = input.color(color.blue, "上漲趨勢顏色", group=ma_group)
ma_color_bear = input.color(color.red, "下跌趨勢顏色", group=ma_group)

// 布林線設定
bb_group = "布林線設定"
show_bb = input.bool(true, "顯示布林線", group=bb_group)
bb_length = input.int(20, "布林線週期", minval=1, group=bb_group)
bb_mult = input.float(2.0, "標準差倍數", minval=0.1, group=bb_group)

// RSI 設定
rsi_group = "RSI 設定"
show_rsi = input.bool(true, "顯示 RSI", group=rsi_group)
rsi_length = input.int(14, "RSI 週期", minval=1, group=rsi_group)
rsi_overbought = input.int(70, "超買水準", minval=50, maxval=100, group=rsi_group)
rsi_oversold = input.int(30, "超賣水準", minval=0, maxval=50, group=rsi_group)

// MACD 設定
macd_group = "MACD 設定"
show_macd = input.bool(true, "顯示 MACD", group=macd_group)
macd_fast = input.int(12, "快線週期", minval=1, group=macd_group)
macd_slow = input.int(26, "慢線週期", minval=1, group=macd_group)
macd_signal = input.int(9, "信號線週期", minval=1, group=macd_group)

// 支撐阻力設定
sr_group = "支撐阻力設定"
show_sr = input.bool(true, "顯示支撐阻力", group=sr_group)
pivot_length = input.int(10, "樞軸點週期", minval=5, group=sr_group)
sr_strength = input.int(3, "支撐阻力強度", minval=1, maxval=10, group=sr_group)

// K線形態設定
pattern_group = "K線形態設定"
show_patterns = input.bool(true, "顯示K線形態", group=pattern_group)
pattern_sensitivity = input.float(0.1, "形態敏感度", minval=0.01, maxval=1.0, group=pattern_group)

// 比特幣專屬設定
btc_group = "比特幣專屬設定"
volatility_threshold = input.float(5.0, "波動性閾值 (%)", minval=1.0, group=btc_group)
volume_spike_mult = input.float(2.0, "成交量異常倍數", minval=1.0, group=btc_group)
show_fibonacci = input.bool(true, "顯示斐波那契回調", group=btc_group)
show_trend_strength = input.bool(true, "顯示趨勢強度", group=btc_group)

// 自動交易信號設定
signal_group = "自動交易信號設定"
enable_signals = input.bool(true, "啟用交易信號", group=signal_group)
signal_sensitivity = input.string("中等", "信號敏感度", options=["高", "中等", "低"], group=signal_group)
require_volume_confirm = input.bool(true, "需要成交量確認", group=signal_group)
require_multiple_confirm = input.bool(true, "需要多重確認", group=signal_group)
show_signal_table = input.bool(true, "顯示信號表格", group=signal_group)

// ==================== 技術指標計算 ====================
// 移動平均線
sma_value = ta.sma(close, sma_length)
ema_value = ta.ema(close, ema_length)

// 布林線
[bb_middle, bb_upper, bb_lower] = ta.bb(close, bb_length, bb_mult)

// RSI
rsi_value = ta.rsi(close, rsi_length)

// MACD
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// 成交量分析
volume_sma = ta.sma(volume, 20)
volume_spike = volume > volume_sma * volume_spike_mult

// ==================== K線形態偵測 ====================
// 基本K線屬性
body_size = math.abs(close - open)
upper_shadow = high - math.max(close, open)
lower_shadow = math.min(close, open) - low
total_range = high - low
body_ratio = total_range > 0 ? body_size / total_range : 0

// 改進的十字星形態 (考慮成交量)
doji = body_ratio < pattern_sensitivity and total_range > 0
doji_star = doji and volume > volume_sma * 1.2

// 改進的鎚子線 (更嚴格的條件)
hammer_basic = lower_shadow > body_size * 2 and upper_shadow < body_size * 0.5
hammer = hammer_basic and low < low[1] and close > open
inverted_hammer = hammer_basic and high > high[1] and close < open

// 改進的射擊之星
shooting_star_basic = upper_shadow > body_size * 2 and lower_shadow < body_size * 0.5
shooting_star = shooting_star_basic and high > high[1] and close < open

// 改進的吞沒形態 (加入成交量確認)
bullish_engulfing_basic = close > open and close[1] < open[1] and open < close[1] and close > open[1]
bullish_engulfing = bullish_engulfing_basic and volume > volume[1] * 1.1
bearish_engulfing_basic = close < open and close[1] > open[1] and open > close[1] and close < open[1]
bearish_engulfing = bearish_engulfing_basic and volume > volume[1] * 1.1

// 新增：晨星和夜星形態
morning_star = close[2] < open[2] and math.abs(close[1] - open[1]) < body_size * 0.3 and close > open and close > (high[2] + low[2]) / 2
evening_star = close[2] > open[2] and math.abs(close[1] - open[1]) < body_size * 0.3 and close < open and close < (high[2] + low[2]) / 2

// ==================== 支撐阻力計算 ====================
// 樞軸點計算
pivot_high = ta.pivothigh(high, pivot_length, pivot_length)
pivot_low = ta.pivotlow(low, pivot_length, pivot_length)

// 支撐阻力線 (改進記憶體管理)
var line[] resistance_lines = array.new<line>()
var line[] support_lines = array.new<line>()

// 限制線條數量避免記憶體問題
max_lines = 20

if not na(pivot_high) and show_sr
    resistance_line = line.new(bar_index - pivot_length, pivot_high, bar_index + 50, pivot_high,
                              color=color.red, width=2, style=line.style_dashed)
    array.push(resistance_lines, resistance_line)
    // 移除過舊的線條
    if array.size(resistance_lines) > max_lines
        old_line = array.shift(resistance_lines)
        line.delete(old_line)

if not na(pivot_low) and show_sr
    support_line = line.new(bar_index - pivot_length, pivot_low, bar_index + 50, pivot_low,
                           color=color.green, width=2, style=line.style_dashed)
    array.push(support_lines, support_line)
    // 移除過舊的線條
    if array.size(support_lines) > max_lines
        old_line = array.shift(support_lines)
        line.delete(old_line)

// ==================== 比特幣專屬分析 ====================
// 價格波動性計算
price_change = math.abs(close - close[1]) / close[1] * 100
high_volatility = price_change > volatility_threshold

// 重大價格波動警報
major_move_up = close > close[1] * (1 + volatility_threshold/100) and volume_spike
major_move_down = close < close[1] * (1 - volatility_threshold/100) and volume_spike

// ==================== 視覺化繪製 ====================
// 移動平均線
plot(show_ma ? sma_value : na, "SMA", color=close > sma_value ? ma_color_bull : ma_color_bear, linewidth=2)
plot(show_ma ? ema_value : na, "EMA", color=close > ema_value ? ma_color_bull : ma_color_bear, linewidth=2)

// 布林線
bb_upper_plot = plot(show_bb ? bb_upper : na, "BB Upper", color=color.gray, linewidth=1)
bb_lower_plot = plot(show_bb ? bb_lower : na, "BB Lower", color=color.gray, linewidth=1)
fill(bb_upper_plot, bb_lower_plot, color=color.new(color.blue, 95), title="BB Fill")

// K線形態標記
plotshape(show_patterns and doji, style=shape.cross, location=location.abovebar,
          color=color.yellow, size=size.small, title="十字星")
plotshape(show_patterns and doji_star, style=shape.xcross, location=location.abovebar,
          color=color.orange, size=size.normal, title="十字星(高量)")
plotshape(show_patterns and hammer, style=shape.triangleup, location=location.belowbar,
          color=color.green, size=size.small, title="鎚子線")
plotshape(show_patterns and inverted_hammer, style=shape.triangleup, location=location.abovebar,
          color=color.blue, size=size.small, title="倒鎚線")
plotshape(show_patterns and shooting_star, style=shape.triangledown, location=location.abovebar,
          color=color.red, size=size.small, title="射擊之星")
plotshape(show_patterns and bullish_engulfing, style=shape.arrowup, location=location.belowbar,
          color=color.lime, size=size.normal, title="看漲吞沒")
plotshape(show_patterns and bearish_engulfing, style=shape.arrowdown, location=location.abovebar,
          color=color.maroon, size=size.normal, title="看跌吞沒")
plotshape(show_patterns and morning_star, style=shape.diamond, location=location.belowbar,
          color=color.aqua, size=size.normal, title="晨星")
plotshape(show_patterns and evening_star, style=shape.diamond, location=location.abovebar,
          color=color.purple, size=size.normal, title="夜星")

// 比特幣專屬警報
plotshape(major_move_up, style=shape.flag, location=location.abovebar, 
          color=color.green, size=size.large, title="重大上漲")
plotshape(major_move_down, style=shape.flag, location=location.belowbar, 
          color=color.red, size=size.large, title="重大下跌")

// 背景顏色 - 高波動性
bgcolor(high_volatility ? color.new(color.orange, 90) : na, title="高波動性背景")

// ==================== 交易信號視覺化 ====================
// 買入信號標記
plotshape(enable_signals and very_strong_buy_signal, style=shape.labelup, location=location.belowbar,
          color=color.new(color.green, 0), textcolor=color.white, size=size.large,
          text="強烈買入", title="強烈買入信號")
plotshape(enable_signals and strong_buy_signal, style=shape.triangleup, location=location.belowbar,
          color=color.new(color.green, 20), size=size.normal, title="買入信號")
plotshape(enable_signals and weak_buy_signal, style=shape.circle, location=location.belowbar,
          color=color.new(color.green, 50), size=size.small, title="弱買入信號")

// 賣出信號標記
plotshape(enable_signals and very_strong_sell_signal, style=shape.labeldown, location=location.abovebar,
          color=color.new(color.red, 0), textcolor=color.white, size=size.large,
          text="強烈賣出", title="強烈賣出信號")
plotshape(enable_signals and strong_sell_signal, style=shape.triangledown, location=location.abovebar,
          color=color.new(color.red, 20), size=size.normal, title="賣出信號")
plotshape(enable_signals and weak_sell_signal, style=shape.circle, location=location.abovebar,
          color=color.new(color.red, 50), size=size.small, title="弱賣出信號")

// ==================== 區域繪製 ====================
// 盤整區域偵測
consolidation_range = input.float(2.0, "盤整區域範圍 (%)", minval=0.5, maxval=10.0, group=btc_group)
lookback_bars = input.int(20, "回看K線數量", minval=10, maxval=100, group=btc_group)

// 計算盤整區域
highest_price = ta.highest(high, lookback_bars)
lowest_price = ta.lowest(low, lookback_bars)
price_range_pct = (highest_price - lowest_price) / lowest_price * 100
is_consolidating = price_range_pct < consolidation_range

// 繪製盤整區域
var box consolidation_box = na
if is_consolidating and not is_consolidating[1]
    consolidation_box := box.new(left=bar_index - lookback_bars, top=highest_price, right=bar_index, bottom=lowest_price,
                                border_color=color.purple, bgcolor=color.new(color.purple, 85),
                                border_width=2, border_style=line.style_dashed)

// ==================== 額外技術指標 ====================
// 斐波那契回調計算 (移到前面避免未定義錯誤)
fib_length = input.int(50, "斐波那契週期", minval=20, group="其他指標")
swing_high = ta.highest(high, fib_length)
swing_low = ta.lowest(low, fib_length)
fib_range = swing_high - swing_low
fib_23_6 = swing_high - fib_range * 0.236
fib_38_2 = swing_high - fib_range * 0.382
fib_50_0 = swing_high - fib_range * 0.500
fib_61_8 = swing_high - fib_range * 0.618

// 成交量加權平均價格 (VWAP)
vwap_value = ta.vwap(hlc3)
plot(vwap_value, "VWAP", color=color.orange, linewidth=2)

// 斐波那契回調線
plot(show_fibonacci ? fib_23_6 : na, "Fib 23.6%", color=color.new(color.yellow, 50), linewidth=1, style=plot.style_circles)
plot(show_fibonacci ? fib_38_2 : na, "Fib 38.2%", color=color.new(color.yellow, 30), linewidth=1, style=plot.style_circles)
plot(show_fibonacci ? fib_50_0 : na, "Fib 50.0%", color=color.new(color.yellow, 10), linewidth=2, style=plot.style_circles)
plot(show_fibonacci ? fib_61_8 : na, "Fib 61.8%", color=color.new(color.yellow, 30), linewidth=1, style=plot.style_circles)

// 平均真實範圍 (ATR)
atr_length = input.int(14, "ATR 週期", minval=1, group="其他指標")
atr_value = ta.atr(atr_length)

// 動量指標
momentum_length = input.int(10, "動量週期", minval=1, group="其他指標")
momentum = close - close[momentum_length]

// 威廉指標 (%R)
williams_length = input.int(14, "威廉指標週期", minval=1, group="其他指標")
williams_r = -100 * (ta.highest(high, williams_length) - close) / (ta.highest(high, williams_length) - ta.lowest(low, williams_length))

// 商品通道指數 (CCI) - 適合比特幣波動性
cci_length = input.int(20, "CCI 週期", minval=1, group="其他指標")
cci_value = ta.cci(close, cci_length)

// 隨機指標 (Stochastic)
stoch_k_length = input.int(14, "隨機指標 K 週期", minval=1, group="其他指標")
stoch_d_length = input.int(3, "隨機指標 D 週期", minval=1, group="其他指標")
stoch_k = ta.stoch(close, high, low, stoch_k_length)
stoch_d = ta.sma(stoch_k, stoch_d_length)

// 趨勢強度指標 (ADX 改進版)
adx_length = input.int(14, "趨勢強度週期", minval=1, group="其他指標")
tr = math.max(high - low, math.max(math.abs(high - close[1]), math.abs(low - close[1])))
plus_dm = high - high[1] > low[1] - low ? math.max(high - high[1], 0) : 0
minus_dm = low[1] - low > high - high[1] ? math.max(low[1] - low, 0) : 0
tr_rma = ta.rma(tr, adx_length)
plus_di = tr_rma > 0 ? 100 * ta.rma(plus_dm, adx_length) / tr_rma : 0
minus_di = tr_rma > 0 ? 100 * ta.rma(minus_dm, adx_length) / tr_rma : 0
di_sum = plus_di + minus_di
dx = di_sum > 0 ? math.abs(plus_di - minus_di) / di_sum * 100 : 0
adx = ta.rma(dx, adx_length)

// 市場結構分析
market_structure_length = input.int(20, "市場結構週期", minval=10, group="其他指標")
higher_high = high > ta.highest(high[1], market_structure_length)
lower_low = low < ta.lowest(low[1], market_structure_length)
uptrend_structure = higher_high and close > ta.sma(close, market_structure_length)
downtrend_structure = lower_low and close < ta.sma(close, market_structure_length)

// ==================== 自動交易信號生成系統 ====================
// 信號敏感度設定
sensitivity_multiplier = signal_sensitivity == "高" ? 0.8 : signal_sensitivity == "中等" ? 1.0 : 1.2

// 基礎技術指標信號
// RSI 信號
rsi_bullish = rsi_value < (rsi_oversold * sensitivity_multiplier) and rsi_value > rsi_value[1]
rsi_bearish = rsi_value > (rsi_overbought * sensitivity_multiplier) and rsi_value < rsi_value[1]

// MACD 信號
macd_bullish_signal = macd_bullish_cross and macd_line > 0
macd_bearish_signal = macd_bearish_cross and macd_line < 0

// 移動平均線信號
ma_bullish = close > sma_value and close > ema_value and sma_value > sma_value[1]
ma_bearish = close < sma_value and close < ema_value and sma_value < sma_value[1]

// 布林線信號
bb_bullish = close < bb_lower and close > close[1] // 下軌反彈
bb_bearish = close > bb_upper and close < close[1] // 上軌回落

// 隨機指標信號
stoch_bullish = stoch_k < 20 and stoch_k > stoch_d and ta.crossover(stoch_k, stoch_d)
stoch_bearish = stoch_k > 80 and stoch_k < stoch_d and ta.crossunder(stoch_k, stoch_d)

// 威廉指標信號
williams_bullish = williams_r < -80 and williams_r > williams_r[1]
williams_bearish = williams_r > -20 and williams_r < williams_r[1]

// CCI 信號
cci_bullish = cci_value < -100 and cci_value > cci_value[1]
cci_bearish = cci_value > 100 and cci_value < cci_value[1]

// K線形態信號
pattern_bullish = hammer or bullish_engulfing or morning_star or (doji_star and rsi_value < 50)
pattern_bearish = shooting_star or bearish_engulfing or evening_star or (doji_star and rsi_value > 50)

// 支撐阻力突破信號
sr_bullish = not na(pivot_low) and close > pivot_low * 1.02 // 突破支撐位2%
sr_bearish = not na(pivot_high) and close < pivot_high * 0.98 // 跌破阻力位2%

// 成交量確認
volume_confirm_bull = not require_volume_confirm or volume > volume_sma * 1.2
volume_confirm_bear = not require_volume_confirm or volume > volume_sma * 1.2

// 趨勢確認
trend_confirm_bull = adx > 20 and plus_di > minus_di
trend_confirm_bear = adx > 20 and minus_di > plus_di

// 綜合信號計算
// 看漲信號強度計算
bullish_score = 0
bullish_score := bullish_score + (rsi_bullish ? 1 : 0)
bullish_score := bullish_score + (macd_bullish_signal ? 2 : 0)
bullish_score := bullish_score + (ma_bullish ? 1 : 0)
bullish_score := bullish_score + (bb_bullish ? 1 : 0)
bullish_score := bullish_score + (stoch_bullish ? 1 : 0)
bullish_score := bullish_score + (williams_bullish ? 1 : 0)
bullish_score := bullish_score + (cci_bullish ? 1 : 0)
bullish_score := bullish_score + (pattern_bullish ? 2 : 0)
bullish_score := bullish_score + (sr_bullish ? 1 : 0)
bullish_score := bullish_score + (volume_confirm_bull ? 1 : 0)
bullish_score := bullish_score + (trend_confirm_bull ? 1 : 0)

// 看跌信號強度計算
bearish_score = 0
bearish_score := bearish_score + (rsi_bearish ? 1 : 0)
bearish_score := bearish_score + (macd_bearish_signal ? 2 : 0)
bearish_score := bearish_score + (ma_bearish ? 1 : 0)
bearish_score := bearish_score + (bb_bearish ? 1 : 0)
bearish_score := bearish_score + (stoch_bearish ? 1 : 0)
bearish_score := bearish_score + (williams_bearish ? 1 : 0)
bearish_score := bearish_score + (cci_bearish ? 1 : 0)
bearish_score := bearish_score + (pattern_bearish ? 2 : 0)
bearish_score := bearish_score + (sr_bearish ? 1 : 0)
bearish_score := bearish_score + (volume_confirm_bear ? 1 : 0)
bearish_score := bearish_score + (trend_confirm_bear ? 1 : 0)

// 信號分級閾值設定
min_score_for_signal = require_multiple_confirm ? 3 : 2
strong_signal_threshold = 6
very_strong_signal_threshold = 8

// 最終交易信號判斷
// 看漲信號
weak_buy_signal = enable_signals and bullish_score >= min_score_for_signal and bullish_score < strong_signal_threshold and bearish_score < 2
strong_buy_signal = enable_signals and bullish_score >= strong_signal_threshold and bullish_score < very_strong_signal_threshold and bearish_score < 3
very_strong_buy_signal = enable_signals and bullish_score >= very_strong_signal_threshold and bearish_score < 2

// 看跌信號
weak_sell_signal = enable_signals and bearish_score >= min_score_for_signal and bearish_score < strong_signal_threshold and bullish_score < 2
strong_sell_signal = enable_signals and bearish_score >= strong_signal_threshold and bearish_score < very_strong_signal_threshold and bullish_score < 3
very_strong_sell_signal = enable_signals and bearish_score >= very_strong_signal_threshold and bullish_score < 2

// 綜合信號狀態
any_buy_signal = weak_buy_signal or strong_buy_signal or very_strong_buy_signal
any_sell_signal = weak_sell_signal or strong_sell_signal or very_strong_sell_signal

// 信號文字描述
signal_text = very_strong_buy_signal ? "強烈買入" :
              strong_buy_signal ? "買入" :
              weak_buy_signal ? "弱買入" :
              very_strong_sell_signal ? "強烈賣出" :
              strong_sell_signal ? "賣出" :
              weak_sell_signal ? "弱賣出" : "觀望"

// 信號顏色
signal_color = very_strong_buy_signal ? color.new(color.green, 0) :
               strong_buy_signal ? color.new(color.green, 30) :
               weak_buy_signal ? color.new(color.green, 60) :
               very_strong_sell_signal ? color.new(color.red, 0) :
               strong_sell_signal ? color.new(color.red, 30) :
               weak_sell_signal ? color.new(color.red, 60) : color.gray

// ==================== 警報條件 ====================
// RSI 超買超賣警報
rsi_overbought_alert = rsi_value > rsi_overbought and rsi_value[1] <= rsi_overbought
rsi_oversold_alert = rsi_value < rsi_oversold and rsi_value[1] >= rsi_oversold

// MACD 金叉死叉
macd_bullish_cross = ta.crossover(macd_line, signal_line)
macd_bearish_cross = ta.crossunder(macd_line, signal_line)

// 布林線突破
bb_upper_break = close > bb_upper and close[1] <= bb_upper
bb_lower_break = close < bb_lower and close[1] >= bb_lower

// ==================== 表格顯示 ====================
// 創建資訊表格
var table info_table = table.new(position.top_right, 3, 12, bgcolor=color.white, border_width=1)

// 創建交易信號表格
var table signal_table = na
if show_signal_table
    signal_table := table.new(position.top_left, 2, 6, bgcolor=color.white, border_width=1)

if barstate.islast
    table.cell(info_table, 0, 0, "指標", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "數值", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 2, 0, "狀態", text_color=color.black, bgcolor=color.gray)

    // RSI 資訊
    table.cell(info_table, 0, 1, "RSI", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(rsi_value, "#.##"), text_color=color.black)
    rsi_status = rsi_value > rsi_overbought ? "超買" : rsi_value < rsi_oversold ? "超賣" : "正常"
    rsi_color = rsi_value > rsi_overbought ? color.red : rsi_value < rsi_oversold ? color.green : color.gray
    table.cell(info_table, 2, 1, rsi_status, text_color=color.white, bgcolor=rsi_color)

    // MACD 資訊
    table.cell(info_table, 0, 2, "MACD", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(macd_line, "#.##"), text_color=color.black)
    macd_status = macd_line > signal_line ? "看漲" : "看跌"
    macd_color = macd_line > signal_line ? color.green : color.red
    table.cell(info_table, 2, 2, macd_status, text_color=color.white, bgcolor=macd_color)

    // 波動性資訊
    table.cell(info_table, 0, 3, "波動性", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(price_change, "#.##") + "%", text_color=color.black)
    vol_status = high_volatility ? "高" : "正常"
    vol_color = high_volatility ? color.orange : color.gray
    table.cell(info_table, 2, 3, vol_status, text_color=color.white, bgcolor=vol_color)

    // 成交量資訊
    table.cell(info_table, 0, 4, "成交量", text_color=color.black)
    volume_ratio = volume / volume_sma
    table.cell(info_table, 1, 4, str.tostring(volume_ratio, "#.##") + "x", text_color=color.black)
    vol_spike_status = volume_spike ? "異常" : "正常"
    vol_spike_color = volume_spike ? color.red : color.gray
    table.cell(info_table, 2, 4, vol_spike_status, text_color=color.white, bgcolor=vol_spike_color)

    // 趨勢強度資訊
    if show_trend_strength
        table.cell(info_table, 0, 5, "趨勢強度", text_color=color.black)
        table.cell(info_table, 1, 5, str.tostring(adx, "#.##"), text_color=color.black)
        trend_status = adx > 25 ? "強勢" : adx > 15 ? "中等" : "弱勢"
        trend_color = adx > 25 ? color.green : adx > 15 ? color.orange : color.gray
        table.cell(info_table, 2, 5, trend_status, text_color=color.white, bgcolor=trend_color)

    // 市場結構資訊
    table.cell(info_table, 0, 6, "市場結構", text_color=color.black)
    structure_status = uptrend_structure ? "上升" : downtrend_structure ? "下降" : "盤整"
    structure_color = uptrend_structure ? color.green : downtrend_structure ? color.red : color.gray
    table.cell(info_table, 1, 6, structure_status, text_color=color.white, bgcolor=structure_color)
    table.cell(info_table, 2, 6, "", text_color=color.black)

    // 交易信號資訊
    if enable_signals
        table.cell(info_table, 0, 7, "交易信號", text_color=color.black)
        table.cell(info_table, 1, 7, signal_text, text_color=color.white, bgcolor=signal_color)
        signal_strength = str.tostring(math.max(bullish_score, bearish_score), "#")
        table.cell(info_table, 2, 7, "強度:" + signal_strength, text_color=color.black)

        // 信號分數詳情
        table.cell(info_table, 0, 8, "看漲分數", text_color=color.black)
        table.cell(info_table, 1, 8, str.tostring(bullish_score, "#"), text_color=color.black)
        table.cell(info_table, 2, 8, "", text_color=color.black)

        table.cell(info_table, 0, 9, "看跌分數", text_color=color.black)
        table.cell(info_table, 1, 9, str.tostring(bearish_score, "#"), text_color=color.black)
        table.cell(info_table, 2, 9, "", text_color=color.black)

// 交易信號詳細表格
if barstate.islast and show_signal_table and enable_signals
    table.cell(signal_table, 0, 0, "交易信號面板", text_color=color.white, bgcolor=color.navy)
    table.cell(signal_table, 1, 0, "", text_color=color.black, bgcolor=color.navy)

    table.cell(signal_table, 0, 1, "當前信號", text_color=color.black)
    table.cell(signal_table, 1, 1, signal_text, text_color=color.white, bgcolor=signal_color)

    table.cell(signal_table, 0, 2, "信號強度", text_color=color.black)
    max_score = math.max(bullish_score, bearish_score)
    strength_text = max_score >= very_strong_signal_threshold ? "非常強" :
                   max_score >= strong_signal_threshold ? "強" :
                   max_score >= min_score_for_signal ? "中等" : "弱"
    table.cell(signal_table, 1, 2, strength_text, text_color=color.black)

    table.cell(signal_table, 0, 3, "建議操作", text_color=color.black)
    action_text = very_strong_buy_signal or strong_buy_signal ? "建議買入" :
                  very_strong_sell_signal or strong_sell_signal ? "建議賣出" :
                  weak_buy_signal ? "考慮買入" :
                  weak_sell_signal ? "考慮賣出" : "持有觀望"
    action_color = (very_strong_buy_signal or strong_buy_signal) ? color.green :
                   (very_strong_sell_signal or strong_sell_signal) ? color.red : color.gray
    table.cell(signal_table, 1, 3, action_text, text_color=color.white, bgcolor=action_color)

    table.cell(signal_table, 0, 4, "風險等級", text_color=color.black)
    risk_text = high_volatility ? "高風險" : adx > 25 ? "中風險" : "低風險"
    risk_color = high_volatility ? color.red : adx > 25 ? color.orange : color.green
    table.cell(signal_table, 1, 4, risk_text, text_color=color.white, bgcolor=risk_color)

    table.cell(signal_table, 0, 5, "確認指標", text_color=color.black)
    confirm_count = (volume_confirm_bull or volume_confirm_bear ? 1 : 0) +
                   (trend_confirm_bull or trend_confirm_bear ? 1 : 0) +
                   (pattern_bullish or pattern_bearish ? 1 : 0)
    confirm_text = str.tostring(confirm_count, "#") + "/3 確認"
    table.cell(signal_table, 1, 5, confirm_text, text_color=color.black)

// ==================== 警報設定 ====================
alertcondition(rsi_overbought_alert, title="RSI 超買警報", message="RSI 進入超買區域")
alertcondition(rsi_oversold_alert, title="RSI 超賣警報", message="RSI 進入超賣區域")
alertcondition(macd_bullish_cross, title="MACD 金叉", message="MACD 出現金叉信號")
alertcondition(macd_bearish_cross, title="MACD 死叉", message="MACD 出現死叉信號")
alertcondition(major_move_up, title="重大上漲", message="比特幣出現重大上漲")
alertcondition(major_move_down, title="重大下跌", message="比特幣出現重大下跌")
alertcondition(bb_upper_break, title="布林線上軌突破", message="價格突破布林線上軌")
alertcondition(bb_lower_break, title="布林線下軌突破", message="價格跌破布林線下軌")
alertcondition(bullish_engulfing, title="看漲吞沒形態", message="出現看漲吞沒K線形態")
alertcondition(bearish_engulfing, title="看跌吞沒形態", message="出現看跌吞沒K線形態")
alertcondition(morning_star, title="晨星形態", message="出現晨星反轉形態")
alertcondition(evening_star, title="夜星形態", message="出現夜星反轉形態")
alertcondition(doji_star, title="高量十字星", message="出現高成交量十字星")
alertcondition(hammer, title="鎚子線形態", message="出現鎚子線反轉信號")
alertcondition(shooting_star, title="射擊之星形態", message="出現射擊之星反轉信號")

// ==================== 交易信號警報 ====================
alertcondition(very_strong_buy_signal, title="強烈買入信號", message="檢測到強烈買入信號！多個指標確認看漲趨勢")
alertcondition(strong_buy_signal, title="買入信號", message="檢測到買入信號，建議考慮進場")
alertcondition(weak_buy_signal, title="弱買入信號", message="檢測到弱買入信號，謹慎考慮")
alertcondition(very_strong_sell_signal, title="強烈賣出信號", message="檢測到強烈賣出信號！多個指標確認看跌趨勢")
alertcondition(strong_sell_signal, title="賣出信號", message="檢測到賣出信號，建議考慮出場")
alertcondition(weak_sell_signal, title="弱賣出信號", message="檢測到弱賣出信號，謹慎考慮")
alertcondition(any_buy_signal and high_volatility, title="高波動買入信號", message="在高波動環境中檢測到買入信號，請注意風險")
alertcondition(any_sell_signal and high_volatility, title="高波動賣出信號", message="在高波動環境中檢測到賣出信號，請注意風險")
